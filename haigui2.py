import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置Tushare Token
your_tushare_token = os.getenv('TUSHARE_TOKEN', '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
ts.set_token(your_tushare_token)
pro = ts.pro_api()

class OptimizedTurtleStrategy:
    """布林带过滤海龟策略 - 收盘价突破20天高价 + 布林带宽度过滤，实现趋势跟进加仓和止盈逐步出货"""
    
    def __init__(self, initial_capital=30000, risk_per_trade=0.01, max_units=4):
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_units = max_units
        self.commission_rate = 0.0003
        self.slippage = 0.001
        
        # 策略参数
        self.sys1_entry = 20
        self.sys1_exit = 10
        self.atr_period = 20

        # 启用布林带宽度过滤条件
        self.enable_filters = True
        self.enable_bollinger_filter = True

        # 布林带宽度过滤参数（调整为更宽松的条件）
        self.bb_period = 20              # 布林带计算周期
        self.bb_std = 2                  # 布林带标准差倍数
        self.bb_width_lookback = 60      # 检查过去N天的布林带宽度
        self.bb_width_percentile = 40    # 布林带宽度需要在过去N天的前40%（即相对较窄的盘整）
        self.bb_squeeze_threshold = 0.20 # 布林带宽度阈值（相对于价格的百分比）

        # 改进的止损策略
        self.initial_stop_loss_n = 3.0        # 初始止损：3N（更宽松）
        self.trailing_stop_loss_n = 2.0       # 盈利后移动止损：2N
        self.profit_threshold = 0.02          # 盈利2%后启用移动止损

        # 改进的加仓策略
        self.add_position_interval = 0.5      # 加仓间隔：0.5N（更合理的间隔）
        self.trend_strength_threshold = 0.3   # 趋势强度阈值，用于判断是否适合加仓

        # 改进的止盈减仓策略（多级别动态止盈）
        self.profit_taking_levels = [3, 5, 8, 12]  # 多级别止盈：3N, 5N, 8N, 12N
        self.profit_taking_ratios = [0.25, 0.3, 0.3, 0.15]  # 对应的减仓比例
        self.min_units_to_hold = 1           # 最少保留单位数
        self.dynamic_profit_adjustment = True # 启用动态止盈调整

        self.reset_state()
    
    def reset_state(self):
        self.cash = self.initial_capital
        self.position = 0
        self.units = 0
        self.entry_prices = []
        self.entry_dates = []
        self.unit_shares = []
        self.last_entry_price = 0
        self.trade_log = []
        self.equity_curve = []
        self.current_n = 0
        self.profit_taking_executed = []  # 记录已执行的止盈减仓级别
        self.filtered_signals = []  # 记录被过滤的信号，用于可视化
        self.last_add_position_date = None  # 记录最后一次加仓日期，避免同日加仓后立即减仓
    
    def calculate_indicators(self, df):
        """计算海龟策略的核心指标和布林带过滤指标"""
        # 基本指标
        df['prev_close'] = df['close'].shift(1)
        df['high_low'] = df['high'] - df['low']
        df['high_prevclose'] = np.abs(df['high'] - df['prev_close'])
        df['low_prevclose'] = np.abs(df['low'] - df['prev_close'])
        df['TR'] = df[['high_low', 'high_prevclose', 'low_prevclose']].max(axis=1)
        df['N'] = df['TR'].rolling(window=self.atr_period).mean()

        # 海龟策略核心信号（修改为收盘价突破）
        df['sys1_entry_signal'] = df['high'].rolling(window=20).max().shift(1)  # 20天高价
        df['sys1_exit_signal'] = df['low'].rolling(window=self.sys1_exit).min().shift(1)

        # 布林带过滤指标
        if self.enable_bollinger_filter:
            # 计算布林带
            df['bb_middle'] = df['close'].rolling(window=self.bb_period).mean()
            df['bb_std'] = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * self.bb_std)
            df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * self.bb_std)

            # 计算布林带宽度（相对于中轨的百分比）
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']

            # 计算过去N天布林带宽度的百分位数
            df['bb_width_percentile'] = df['bb_width'].rolling(window=self.bb_width_lookback).rank(pct=True) * 100

            # 检查当前布林带宽度是否足够窄（表示盘整）
            df['is_bb_squeeze'] = df['bb_width'] < self.bb_squeeze_threshold

            # 检查布林带宽度是否在历史低位（表示长期盘整）
            df['is_bb_narrow'] = df['bb_width_percentile'] <= self.bb_width_percentile

        return df
    
    def check_filters(self, row, df, current_index):
        """布林带宽度过滤条件"""
        if not self.enable_filters or not self.enable_bollinger_filter:
            return True, "纯海龟策略(无过滤)"

        # 检查是否有足够的历史数据
        if current_index < max(self.bb_period, self.bb_width_lookback):
            return False, "历史数据不足"

        # 1. 检查当前布林带宽度
        bb_width = row['bb_width']
        if pd.isna(bb_width):
            return False, "布林带宽度计算失败"

        # 2. 检查布林带宽度是否在历史低位（表示相对盘整）
        bb_width_percentile = row['bb_width_percentile']
        if pd.isna(bb_width_percentile):
            return False, "布林带宽度百分位计算失败"

        # 使用更灵活的条件：要么宽度在历史低位，要么绝对宽度较小
        condition1 = bb_width_percentile <= self.bb_width_percentile  # 历史低位
        condition2 = bb_width <= self.bb_squeeze_threshold            # 绝对宽度小

        if not (condition1 or condition2):
            return False, f"布林带宽度过大(历史{bb_width_percentile:.1f}%位,宽度{bb_width*100:.2f}%)"

        # 3. 检查过去一段时间的平均布林带宽度（确保经历了盘整期）
        lookback_days = 10  # 检查过去10天
        start_idx = max(0, current_index - lookback_days)
        recent_data = df.iloc[start_idx:current_index+1]

        avg_bb_width = recent_data['bb_width'].mean()
        if pd.isna(avg_bb_width) or avg_bb_width > self.bb_squeeze_threshold * 1.5:
            return False, f"近期平均布林带宽度过大({avg_bb_width*100:.2f}%)"

        # 通过所有条件
        reason_parts = []
        if condition1:
            reason_parts.append(f"历史{bb_width_percentile:.1f}%位")
        if condition2:
            reason_parts.append(f"宽度{bb_width*100:.2f}%")
        reason_parts.append(f"近期均宽{avg_bb_width*100:.2f}%")

        return True, f"布林带突破({','.join(reason_parts)})"
    
    def calculate_position_size(self, current_n, current_price):
        if current_n <= 0:
            return 0
        current_equity = self.cash + self.position * current_price
        risk_amount = current_equity * self.risk_per_trade
        unit_value = risk_amount / current_n

        # 计算股数，确保至少买入100股
        raw_shares = unit_value / current_price
        shares = max(int(raw_shares / 100) * 100, 100)  # 至少100股，且是100的倍数

        # 检查是否有足够资金
        required_cash = shares * current_price * (1 + self.commission_rate + self.slippage)
        if required_cash > self.cash:
            # 如果资金不足，计算能买的最大股数
            max_affordable = int(self.cash / (current_price * (1 + self.commission_rate + self.slippage)) / 100) * 100
            shares = max(max_affordable, 0)

        return shares
    
    def should_add_position(self, current_high, current_close, df, current_index):
        """改进的加仓逻辑：考虑趋势强度、合理间隔和连贯涨势"""
        if self.units >= self.max_units or self.units == 0:
            return False, 0

        # 使用更合理的加仓间隔
        next_add_price = self.last_entry_price + self.add_position_interval * self.current_n

        # 检查趋势强度：价格应该持续上涨
        if len(self.entry_prices) > 0:
            avg_entry_price = np.mean(self.entry_prices)
            trend_strength = (current_close - avg_entry_price) / (self.current_n * self.units)

            # 只有在趋势足够强劲时才加仓
            if trend_strength < self.trend_strength_threshold:
                return False, 0

        # 新增：检查连贯涨势，防止在趋势反转后加仓
        if not self._check_continuous_uptrend(df, current_index, current_close):
            return False, 0

        if current_high >= next_add_price:
            return True, next_add_price
        return False, 0

    def _check_continuous_uptrend(self, df, current_index, current_close):
        """检查连贯涨势：确保在连续上涨趋势中加仓，避免趋势反转后加仓"""
        # 需要至少5天的数据来判断趋势
        lookback_days = min(5, current_index)
        if lookback_days < 3:
            return True  # 数据不足时默认通过

        # 获取最近几天的收盘价
        start_idx = current_index - lookback_days
        recent_closes = df.iloc[start_idx:current_index+1]['close'].values

        if len(recent_closes) < 3:
            return True

        # 检查1：最近3天是否有连续下跌
        last_3_days = recent_closes[-3:]
        consecutive_decline = all(last_3_days[i] > last_3_days[i+1] for i in range(len(last_3_days)-1))
        if consecutive_decline:
            return False  # 连续下跌，不适合加仓

        # 检查2：当前价格是否低于最近加仓价格的一定比例
        if hasattr(self, 'last_entry_price') and self.last_entry_price > 0:
            price_decline_from_last_entry = (self.last_entry_price - current_close) / self.last_entry_price
            if price_decline_from_last_entry > 0.02:  # 如果相比上次加仓价格下跌超过2%
                return False

        # 检查3：短期趋势方向（3日均线vs5日均线）
        if len(recent_closes) >= 5:
            ma3 = np.mean(recent_closes[-3:])
            ma5 = np.mean(recent_closes[-5:])
            if ma3 < ma5 * 0.998:  # 短期均线明显低于中期均线
                return False

        # 检查4：价格动量（当前价格相对于N天前的变化）
        if len(recent_closes) >= 4:
            momentum = (current_close - recent_closes[-4]) / recent_closes[-4]
            if momentum < -0.01:  # 如果4天内下跌超过1%
                return False

        return True  # 通过所有检查，可以加仓
    
    def calculate_stop_loss(self, current_n, current_price):
        """改进的止损计算"""
        if self.units == 0:
            return 0
        
        avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
        current_return = (current_price - avg_entry_price) / avg_entry_price if avg_entry_price > 0 else 0
        
        # 如果盈利超过阈值，使用移动止损
        if current_return > self.profit_threshold:
            # 移动止损：保护利润
            trailing_stop = current_price - self.trailing_stop_loss_n * current_n
            initial_stop = avg_entry_price - self.initial_stop_loss_n * current_n
            stop_price = max(trailing_stop, initial_stop)
            print(f"启用移动止损: 当前收益{current_return*100:.1f}%, 止损价格{stop_price:.2f}")
        else:
            # 初始止损：更宽松的3N
            stop_price = avg_entry_price - self.initial_stop_loss_n * current_n
        
        return stop_price

    def check_profit_taking(self, current_price, current_n):
        """改进的多级别动态止盈减仓策略"""
        if self.units <= self.min_units_to_hold or not self.entry_prices:
            return False, 0, ""

        # 计算平均入场价格和当前盈利
        avg_entry_price = np.mean(self.entry_prices)
        profit_n = (current_price - avg_entry_price) / current_n
        profit_percentage = (current_price - avg_entry_price) / avg_entry_price

        # 动态调整止盈级别（根据波动性）
        if self.dynamic_profit_adjustment:
            # 如果波动性较大，适当放宽止盈条件
            volatility_factor = min(current_n / avg_entry_price, 0.1)  # 限制在10%以内
            adjusted_levels = [level * (1 - volatility_factor) for level in self.profit_taking_levels]
        else:
            adjusted_levels = self.profit_taking_levels

        # 检查各个止盈级别
        for i, level in enumerate(adjusted_levels):
            if profit_n >= level and level not in self.profit_taking_executed:
                # 计算减仓数量（按比例减仓，而不是固定减仓一个单位）
                total_position = self.position
                reduction_ratio = self.profit_taking_ratios[i] if i < len(self.profit_taking_ratios) else 0.25
                shares_to_sell = int(total_position * reduction_ratio)

                # 确保不会减仓过多
                remaining_shares = total_position - shares_to_sell
                min_shares_to_keep = sum(self.unit_shares[:self.min_units_to_hold]) if len(self.unit_shares) >= self.min_units_to_hold else 0

                if remaining_shares < min_shares_to_keep:
                    shares_to_sell = total_position - min_shares_to_keep

                if shares_to_sell > 0:
                    reason = f"{self.profit_taking_levels[i]}N止盈减仓({reduction_ratio*100:.0f}%)"
                    return True, shares_to_sell, reason

        return False, 0, ""

    def execute_partial_exit(self, date, price, shares_to_sell, reason):
        """改进的部分平仓逻辑 - 支持按比例减仓"""
        if shares_to_sell >= self.position:
            # 如果要卖出的股数大于等于总持仓，则全部平仓
            return self.execute_trade(date, 'SELL', price, 0, reason)

        # 部分平仓
        proceeds = shares_to_sell * price * (1 - self.commission_rate - self.slippage)
        self.cash += proceeds
        self.position -= shares_to_sell

        # 按比例减少各个单位的持仓（而不是完全移除某个单位）
        total_profit_loss = 0
        reduction_ratio = shares_to_sell / (self.position + shares_to_sell)  # 原始持仓比例

        # 按比例减少每个单位
        units_to_remove = []
        for i in range(len(self.unit_shares)):
            unit_reduction = int(self.unit_shares[i] * reduction_ratio)
            if unit_reduction > 0:
                avg_entry_price = self.entry_prices[i]
                unit_profit_loss = unit_reduction * (price - avg_entry_price)
                total_profit_loss += unit_profit_loss

                self.unit_shares[i] -= unit_reduction

                # 如果某个单位的股数减少到0，则移除该单位
                if self.unit_shares[i] <= 0:
                    units_to_remove.append(i)

        # 移除股数为0的单位（从后往前移除，避免索引问题）
        for i in reversed(units_to_remove):
            self.entry_prices.pop(i)
            self.entry_dates.pop(i)
            self.unit_shares.pop(i)
            self.units -= 1

        # 记录止盈级别
        try:
            level_str = reason.split('N')[0]
            level = float(level_str)
            if level not in self.profit_taking_executed:
                self.profit_taking_executed.append(level)
        except (ValueError, IndexError):
            pass  # 如果解析失败，忽略

        self.trade_log.append({
            'date': date, 'action': 'PARTIAL_SELL', 'price': price,
            'shares': shares_to_sell, 'units': self.units, 'cash': self.cash,
            'reason': reason, 'profit_loss': total_profit_loss
        })

        print(f"止盈减仓: {date.strftime('%Y-%m-%d')}, 价格: {price:.2f}, "
              f"减仓: {shares_to_sell}股({reduction_ratio*100:.1f}%), 剩余单位: {self.units}, 原因: {reason}")

        return True

    def execute_trade(self, date, action, price, shares, reason=""):
        if action == 'BUY':
            cost = shares * price * (1 + self.commission_rate + self.slippage)
            if cost <= self.cash:
                self.cash -= cost
                self.position += shares

                # 区分初始买入和加仓
                if self.position == shares:  # 这是初始买入
                    self.units = 1
                    self.entry_prices = [price]  # 重置入场价格列表
                    self.entry_dates = [date]    # 重置入场日期列表
                    self.unit_shares = [shares]  # 重置单位股数列表
                    trade_type = "初始买入"
                else:  # 这是加仓
                    self.units += 1
                    self.entry_prices.append(price)
                    self.entry_dates.append(date)
                    self.unit_shares.append(shares)
                    trade_type = "趋势加仓"

                self.last_entry_price = price

                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': shares, 'units': self.units, 'cash': self.cash,
                    'reason': reason, 'trade_type': trade_type
                })
                print(f"{trade_type}: {date.strftime('%Y-%m-%d')}, 价格: {price:.2f}, "
                      f"股数: {shares}, 单位: {self.units}, 原因: {reason}")
                return True
        
        elif action == 'SELL':
            if self.position > 0:
                avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
                total_return = (price - avg_entry_price) / avg_entry_price if avg_entry_price > 0 else 0
                profit_loss = self.position * (price - avg_entry_price)
                
                proceeds = self.position * price * (1 - self.commission_rate - self.slippage)
                self.cash += proceeds
                
                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': self.position, 'units': self.units, 'cash': self.cash,
                    'reason': reason, 'profit_loss': profit_loss, 'return_rate': total_return
                })
                
                print(f"卖出: {date.strftime('%Y-%m-%d')}, 价格: {price:.2f}, "
                      f"收益率: {total_return*100:+.2f}%, 盈亏: {profit_loss:+.2f}元, 原因: {reason}")
                
                self.position = 0
                self.units = 0
                self.entry_prices = []
                self.entry_dates = []
                self.unit_shares = []
                self.last_entry_price = 0
                self.profit_taking_executed = []
                self.last_add_position_date = None  # 重置加仓日期
                return True
        
        return False
    
    def backtest(self, df):
        strategy_name = "收盘价突破20天高价+布林带过滤策略" if self.enable_bollinger_filter else "纯海龟策略"
        print(f"开始{strategy_name}回测...")
        self.reset_state()
        df = self.calculate_indicators(df)

        trade_count = 0
        signal_count = 0
        filter_rejected_count = 0

        for idx, (i, row) in enumerate(df.iterrows()):
            current_date = i  # i 就是日期索引

            if pd.isna(row['N']) or pd.isna(row['sys1_entry_signal']) or pd.isna(row['sys1_exit_signal']):
                current_equity = self.cash + self.position * row['close']
                self.equity_curve.append(current_equity)
                continue

            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            self.current_n = row['N']

            # 持仓时的各种检查
            if self.position > 0:
                # 1. 首先检查止损（最高优先级）
                stop_loss_price = self.calculate_stop_loss(self.current_n, current_close)
                if current_low <= stop_loss_price:
                    avg_entry = np.mean(self.entry_prices) if self.entry_prices else 0
                    current_return = (current_close - avg_entry) / avg_entry if avg_entry > 0 else 0
                    if current_return > self.profit_threshold:
                        reason = f"移动止损(盈利{current_return*100:.1f}%)"
                    else:
                        reason = f"初始止损({self.initial_stop_loss_n}N)"
                    self.execute_trade(current_date, 'SELL', stop_loss_price, 0, reason)

                # 2. 如果没有止损，检查加仓机会
                elif self.units < self.max_units:
                    should_add, add_price = self.should_add_position(current_high, current_close, df, idx)
                    if should_add:
                        shares = self.calculate_position_size(self.current_n, add_price)
                        if shares > 0:
                            success = self.execute_trade(current_date, 'BUY', add_price, shares, f"趋势加仓")
                            if success:
                                self.last_add_position_date = current_date  # 记录加仓日期

                # 3. 检查止盈减仓（只有在多个单位时才检查，且避免在加仓当天立即减仓）
                if (self.position > 0 and self.units > 1 and
                    (self.last_add_position_date is None or self.last_add_position_date != current_date)):
                    should_take_profit, shares_to_sell, profit_reason = self.check_profit_taking(current_close, self.current_n)
                    if should_take_profit:
                        self.execute_partial_exit(current_date, current_close, shares_to_sell, profit_reason)

                # 4. 检查趋势跟踪出场（最后检查）
                if self.position > 0 and current_low <= row['sys1_exit_signal']:
                    self.execute_trade(current_date, 'SELL', row['sys1_exit_signal'], 0, "系统1出场")

            # 入场信号检查（收盘价高于20天高价 + 布林带过滤）
            elif self.position == 0 and row['close'] > row['sys1_entry_signal']:
                signal_count += 1

                # 应用过滤条件
                filter_passed, filter_reason = self.check_filters(row, df, idx)

                if filter_passed:
                    shares = self.calculate_position_size(self.current_n, current_close)
                    print(f"🔍 入场信号触发 {current_date.strftime('%Y-%m-%d')}: 收盘{current_close:.2f} > 20天高价{row['sys1_entry_signal']:.2f}, N={self.current_n:.2f}, 计算股数={shares}")
                    print(f"✅ 过滤条件通过: {filter_reason}")

                    if shares > 0:
                        reason = f"系统1入场-{filter_reason}"
                        success = self.execute_trade(current_date, 'BUY', current_close, shares, reason)
                        if success:
                            trade_count += 1
                            print(f"✅ 交易执行成功，总交易数: {trade_count}")
                        else:
                            print(f"❌ 交易执行失败")
                    else:
                        print(f"⚠️ 计算的股数为0，无法交易")
                else:
                    filter_rejected_count += 1
                    # 记录被过滤的信号用于可视化
                    self.filtered_signals.append({
                        'date': current_date,
                        'price': current_close,
                        'signal_price': row['sys1_entry_signal'],
                        'reason': filter_reason
                    })
                    if signal_count <= 10:  # 只显示前10个被过滤的信号，避免输出过多
                        print(f"❌ 入场信号被过滤 {current_date.strftime('%Y-%m-%d')}: {filter_reason}")

            # 记录每日净资产
            current_equity = self.cash + self.position * current_close
            self.equity_curve.append(current_equity)

        print(f"回测完成: 信号触发{signal_count}次, 过滤拒绝{filter_rejected_count}次, 实际交易{trade_count}次")
        if self.enable_bollinger_filter:
            filter_success_rate = (signal_count - filter_rejected_count) / signal_count * 100 if signal_count > 0 else 0
            print(f"过滤通过率: {filter_success_rate:.1f}% ({signal_count - filter_rejected_count}/{signal_count})")
        return df
    
    def print_results(self):
        if not self.equity_curve:
            print("错误: 没有回测数据")
            return
        # 不在这里打印结果，改为生成报告
        self.generate_report()

    def generate_report(self):
        """生成一目了然的策略回测报告"""
        if not self.equity_curve:
            print("❌ 没有回测数据，无法生成报告")
            return None

        final_equity = self.equity_curve[-1]
        total_return = (final_equity - self.initial_capital) / self.initial_capital * 100

        # 计算基本统计
        buy_trades = [t for t in self.trade_log if t['action'] == 'BUY']
        sell_trades = [t for t in self.trade_log if t['action'] == 'SELL']
        partial_sell_trades = [t for t in self.trade_log if t['action'] == 'PARTIAL_SELL']
        winning_trades = [t for t in sell_trades if t.get('return_rate', 0) > 0]
        losing_trades = [t for t in sell_trades if t.get('return_rate', 0) <= 0]

        # 如果没有交易，直接返回简单报告
        if not self.trade_log:
            print(f"\n📊 回测完成！")
            print(f"💰 总收益率: {total_return:.2f}% | 交易次数: 0 | 最大回撤: 0.0%")
            print("⚠️ 整个回测期间没有产生任何交易")
            return None

        # 计算性能指标
        equity_series = pd.Series(self.equity_curve)
        returns = equity_series.pct_change().dropna()
        annual_return = returns.mean() * 252 if len(returns) > 0 else 0
        annual_volatility = returns.std() * np.sqrt(252) if len(returns) > 0 else 0
        peak = equity_series.cummax()
        drawdown = (peak - equity_series) / peak
        max_drawdown = drawdown.max() if len(drawdown) > 0 else 0
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0

        # 计算盈亏统计
        total_profit = sum([t.get('profit_loss', 0) for t in winning_trades])
        total_loss = sum([t.get('profit_loss', 0) for t in losing_trades])
        partial_profit = sum([t.get('profit_loss', 0) for t in partial_sell_trades])

        # 计算持仓时间
        holding_periods = []
        for i, sell_trade in enumerate(sell_trades):
            sell_date = sell_trade['date']
            buy_date = None
            for buy_trade in reversed(buy_trades[:i+1]):
                if '入场' in buy_trade.get('reason', ''):
                    buy_date = buy_trade['date']
                    break
            if buy_date:
                holding_days = (sell_date - buy_date).days
                holding_periods.append(holding_days)

        # 生成报告内容
        report_content = self._format_report(
            final_equity, total_return, buy_trades, sell_trades, partial_sell_trades,
            winning_trades, losing_trades, annual_return, annual_volatility,
            max_drawdown, sharpe_ratio, total_profit, total_loss, partial_profit,
            holding_periods
        )

        # 保存报告到文件
        report_filename = f"海龟策略回测报告_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # 同时在控制台显示简化版本
        print(f"\n📊 回测完成！详细报告已保存至: {report_filename}")
        win_rate = len(winning_trades)/len(sell_trades)*100 if sell_trades else 0
        print(f"💰 总收益率: {total_return:.2f}% | 胜率: {win_rate:.1f}% | 最大回撤: {max_drawdown*100:.1f}%")

        return report_filename

    def _format_report(self, final_equity, total_return, buy_trades, sell_trades, partial_sell_trades,
                      winning_trades, losing_trades, annual_return, annual_volatility,
                      max_drawdown, sharpe_ratio, total_profit, total_loss, partial_profit,
                      holding_periods):
        """格式化报告内容"""

        # 获取回测时间范围
        start_date = self.trade_log[0]['date'].strftime('%Y-%m-%d') if self.trade_log else "N/A"
        end_date = self.trade_log[-1]['date'].strftime('%Y-%m-%d') if self.trade_log else "N/A"

        strategy_desc = "收盘价突破20天高价+布林带过滤" if self.enable_bollinger_filter else "纯海龟策略"
        report = f"""
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                                  海龟策略回测报告                                      ║
║                                 ({strategy_desc})                                     ║
╠══════════════════════════════════════════════════════════════════════════════════════╣
║ 报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}                                                    ║
║ 回测时间范围: {start_date} 至 {end_date}                                        ║
╚══════════════════════════════════════════════════════════════════════════════════════╝

📊 核心业绩指标
{'='*80}
💰 资金表现
   初始资金:        {self.initial_capital:>15,.2f} 元
   最终资产:        {final_equity:>15,.2f} 元
   绝对收益:        {final_equity - self.initial_capital:>15,.2f} 元
   总收益率:        {total_return:>15.2f}%
   年化收益率:      {annual_return*100:>15.2f}%

📈 风险指标
   年化波动率:      {annual_volatility*100:>15.2f}%
   最大回撤:        {max_drawdown*100:>15.2f}%
   夏普比率:        {sharpe_ratio:>15.2f}

🎯 交易统计
   总交易次数:      {len(buy_trades):>15} 笔
   完成交易:        {len(sell_trades):>15} 笔
   盈利交易:        {len(winning_trades):>15} 笔
   亏损交易:        {len(losing_trades):>15} 笔
   胜率:            {len(winning_trades)/len(sell_trades)*100 if sell_trades else 0:>15.1f}%

💵 盈亏分析
   总盈利:          {total_profit:>15,.2f} 元
   总亏损:          {total_loss:>15,.2f} 元
   净盈亏:          {total_profit + total_loss:>15,.2f} 元
   止盈减仓盈利:    {partial_profit:>15,.2f} 元
   止盈减仓次数:    {len(partial_sell_trades):>15} 次

   平均盈利:        {total_profit/len(winning_trades) if winning_trades else 0:>15,.2f} 元
   平均亏损:        {total_loss/len(losing_trades) if losing_trades else 0:>15,.2f} 元
   盈亏比:          {abs(total_profit/total_loss) if total_loss != 0 else 0:>15.2f}

⏱️ 持仓分析
   平均持仓天数:    {np.mean(holding_periods) if holding_periods else 0:>15.1f} 天
   最长持仓:        {max(holding_periods) if holding_periods else 0:>15} 天
   最短持仓:        {min(holding_periods) if holding_periods else 0:>15} 天

"""

        # 添加出场原因统计
        exit_reasons = {}
        all_exit_trades = sell_trades + partial_sell_trades
        for trade in all_exit_trades:
            reason = trade.get('reason', '未知')
            exit_reasons[reason] = exit_reasons.get(reason, 0) + 1

        report += "📋 出场原因分析\n"
        report += "="*80 + "\n"
        for reason, count in sorted(exit_reasons.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(all_exit_trades) * 100
            report += f"   {reason:<25} {count:>8} 笔 ({percentage:>5.1f}%)\n"

        # 添加详细交易记录日志
        report += f"""
📝 详细交易记录日志
{'='*80}
"""

        # 按日期排序所有交易记录
        all_trades = sorted(self.trade_log, key=lambda x: x['date'])

        current_position_group = 0
        for i, trade in enumerate(all_trades):
            date_str = trade['date'].strftime('%Y-%m-%d')
            action = trade['action']
            price = trade['price']

            if action == 'BUY':
                if '入场' in trade.get('reason', ''):
                    current_position_group += 1
                    report += f"\n🔵 第{current_position_group}轮交易 - 初始入场\n"
                    report += f"   📅 {date_str} | 💰 {price:.2f}元 | 📊 {trade['shares']}股 | 💵 成本{trade['shares']*price:.0f}元\n"
                    report += f"   📋 原因: {trade.get('reason', '未知')}\n"
                    report += f"   💼 单位数: {trade['units']} | 💸 剩余现金: {trade['cash']:.0f}元\n"
                else:
                    report += f"   ➕ {date_str} | 💰 {price:.2f}元 | 📊 {trade['shares']}股 | 💵 成本{trade['shares']*price:.0f}元\n"
                    report += f"   📋 {trade.get('reason', '未知')} | 💼 单位数: {trade['units']}\n"

            elif action == 'PARTIAL_SELL':
                profit_loss = trade.get('profit_loss', 0)
                report += f"   📤 {date_str} | 💰 {price:.2f}元 | 📊 减仓{trade['shares']}股 | 💵 收入{trade['shares']*price:.0f}元\n"
                report += f"   📋 {trade.get('reason', '未知')} | 💰 盈亏: {profit_loss:+.0f}元\n"

            elif action == 'SELL':
                profit_loss = trade.get('profit_loss', 0)
                return_rate = trade.get('return_rate', 0)
                report += f"   🔴 {date_str} | 💰 {price:.2f}元 | 📊 全部平仓{trade['shares']}股\n"
                report += f"   📋 {trade.get('reason', '未知')} | 💰 盈亏: {profit_loss:+.0f}元 | 📈 收益率: {return_rate*100:+.1f}%\n"
                report += f"   💸 最终现金: {trade['cash']:.0f}元\n"

        # 添加策略参数
        report += f"""
⚙️ 策略参数设置
{'='*80}
   入场信号:        {self.sys1_entry} 日高点突破
   出场信号:        {self.sys1_exit} 日低点突破
   ATR周期:         {self.atr_period} 日
   最大单位数:      {self.max_units} 个
   每笔风险:        {self.risk_per_trade*100:.1f}%
   初始止损:        {self.initial_stop_loss_n}N
   移动止损:        {self.trailing_stop_loss_n}N
   加仓间隔:        {self.add_position_interval}N
   止盈级别:        {', '.join([f'{level}N' for level in self.profit_taking_levels])}

📝 策略特点
{'='*80}
✓ 收盘价突破20天高价入场信号
✓ 布林带宽度过滤识别有效突破
✓ 识别长期盘整后的有效突破
✓ 支持趋势跟进加仓（最多{self.max_units}个单位）
✓ 多级别动态止盈减仓
✓ 移动止损保护利润
✓ 严格的风险控制

🔍 布林带过滤参数
{'='*80}
   布林带周期:        {self.bb_period} 天
   标准差倍数:        {self.bb_std}
   宽度检查期:        {self.bb_width_lookback} 天
   宽度百分位要求:    前{self.bb_width_percentile}%（较窄盘整）
   宽度阈值:          {self.bb_squeeze_threshold*100:.1f}%
   成交量确认:        {'是' if getattr(self, 'volume_confirmation', False) else '否'}

🚫 信号过滤统计
{'='*80}"""

        # 添加过滤信号统计
        if hasattr(self, 'filtered_signals') and self.filtered_signals:
            total_signals = len(self.filtered_signals) + len([t for t in buy_trades if '入场' in t.get('reason', '')])
            filter_success_rate = len([t for t in buy_trades if '入场' in t.get('reason', '')]) / total_signals * 100 if total_signals > 0 else 0

            report += f"""   总信号数量:        {total_signals:>15} 个
   通过信号数量:      {len([t for t in buy_trades if '入场' in t.get('reason', '')]):>15} 个
   过滤信号数量:      {len(self.filtered_signals):>15} 个
   信号通过率:        {filter_success_rate:>15.1f}%

📋 被过滤信号详情:
"""

            # 统计过滤原因
            filter_reasons = {}
            for signal in self.filtered_signals:
                reason = signal['reason']
                filter_reasons[reason] = filter_reasons.get(reason, 0) + 1

            for reason, count in sorted(filter_reasons.items(), key=lambda x: x[1], reverse=True):
                percentage = count / len(self.filtered_signals) * 100
                report += f"   {reason:<35} {count:>5} 次 ({percentage:>5.1f}%)\n"

            # 显示前10个被过滤的信号详情
            report += f"\n📅 被过滤信号时间记录 (前10个):\n"
            for i, signal in enumerate(self.filtered_signals[:10]):
                date_str = signal['date'].strftime('%Y-%m-%d')
                report += f"   {i+1:>2}. {date_str} | 价格: {signal['price']:>6.2f} | 突破: {signal['signal_price']:>6.2f} | {signal['reason']}\n"

            if len(self.filtered_signals) > 10:
                report += f"   ... 还有 {len(self.filtered_signals) - 10} 个被过滤信号\n"
        else:
            report += f"   无过滤信号记录 (可能未启用过滤或无信号被过滤)\n"

        report += f"""
⚠️ 风险提示
{'='*80}
• 本报告基于历史数据回测，不代表未来表现
• 实际交易中可能存在滑点、手续费等额外成本
• 市场环境变化可能影响策略有效性
• 建议结合实际情况调整参数和仓位管理
• 过滤条件的调整会显著影响交易频率和收益

{'='*80}
报告结束 - 海龟策略回测系统
"""

        return report


def plot_results(df, turtle):
    """绘制海龟策略回测结果图表"""
    # 添加资金曲线到DataFrame
    df = df.iloc[:len(turtle.equity_curve)].copy()
    df['equity'] = turtle.equity_curve

    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))

    # 1. 资金曲线图
    ax1.plot(df.index, df['equity'], label='资金曲线', linewidth=2, color='blue')
    ax1.axhline(y=turtle.initial_capital, color='gray', linestyle='--', alpha=0.7, label='初始资金')

    # 标记重要里程碑
    final_equity = turtle.equity_curve[-1]
    max_equity = max(turtle.equity_curve)
    min_equity = min(turtle.equity_curve)

    ax1.axhline(y=max_equity, color='green', linestyle=':', alpha=0.5, label=f'最高点: {max_equity:,.0f}')
    ax1.axhline(y=min_equity, color='red', linestyle=':', alpha=0.5, label=f'最低点: {min_equity:,.0f}')

    ax1.set_title('海龟策略资金曲线', fontsize=14, fontweight='bold')
    ax1.set_ylabel('资产价值 (元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 价格和交易信号图
    ax2.plot(df.index, df['close'], label='股价', linewidth=1.5, color='black', alpha=0.8)

    # 绘制信号线
    if 'sys1_entry_signal' in df.columns:
        ax2.plot(df.index, df['sys1_entry_signal'], label='系统1入场线(20日高点)',
                linestyle='--', linewidth=1, color='red', alpha=0.7)
    if 'sys1_exit_signal' in df.columns:
        ax2.plot(df.index, df['sys1_exit_signal'], label='系统1出场线(10日低点)',
                linestyle='--', linewidth=1, color='green', alpha=0.7)
    if 'ma_trend' in df.columns:
        ax2.plot(df.index, df['ma_trend'], label=f'{turtle.trend_ma_period}日趋势线',
                linestyle=':', linewidth=1, color='orange', alpha=0.7)

    # 标记买入卖出点
    buy_trades = [t for t in turtle.trade_log if t['action'] == 'BUY']
    sell_trades = [t for t in turtle.trade_log if t['action'] == 'SELL']
    partial_sell_trades = [t for t in turtle.trade_log if t['action'] == 'PARTIAL_SELL']

    if buy_trades:
        buy_dates = [t['date'] for t in buy_trades]
        buy_prices = [t['price'] for t in buy_trades]
        buy_reasons = [t.get('reason', '') for t in buy_trades]

        # 区分首次入场和加仓
        first_entry = [i for i, r in enumerate(buy_reasons) if '入场' in r]
        add_position = [i for i, r in enumerate(buy_reasons) if '加仓' in r]

        if first_entry:
            ax2.scatter([buy_dates[i] for i in first_entry],
                       [buy_prices[i] for i in first_entry],
                       color='green', marker='^', s=120, label='首次入场', zorder=5)
        if add_position:
            ax2.scatter([buy_dates[i] for i in add_position],
                       [buy_prices[i] for i in add_position],
                       color='lightgreen', marker='^', s=80, label='加仓', zorder=5)

    if sell_trades:
        sell_dates = [t['date'] for t in sell_trades]
        sell_prices = [t['price'] for t in sell_trades]
        sell_reasons = [t.get('reason', '') for t in sell_trades]

        # 区分不同出场原因
        stop_loss = [i for i, r in enumerate(sell_reasons) if '止损' in r]
        normal_exit = [i for i, r in enumerate(sell_reasons) if '出场' in r]

        if stop_loss:
            ax2.scatter([sell_dates[i] for i in stop_loss],
                       [sell_prices[i] for i in stop_loss],
                       color='red', marker='v', s=120, label='止损', zorder=5)
        if normal_exit:
            ax2.scatter([sell_dates[i] for i in normal_exit],
                       [sell_prices[i] for i in normal_exit],
                       color='orange', marker='v', s=100, label='正常出场', zorder=5)

    # 标记止盈减仓点
    if partial_sell_trades:
        partial_dates = [t['date'] for t in partial_sell_trades]
        partial_prices = [t['price'] for t in partial_sell_trades]
        ax2.scatter(partial_dates, partial_prices,
                   color='purple', marker='s', s=80, label='止盈减仓', zorder=5)

    # 标记被过滤的信号点（用虚线圆圈表示）
    if turtle.filtered_signals:
        filtered_dates = [s['date'] for s in turtle.filtered_signals]
        filtered_prices = [s['price'] for s in turtle.filtered_signals]
        ax2.scatter(filtered_dates, filtered_prices,
                   facecolors='none', edgecolors='gray', marker='o', s=60,
                   linestyle='--', linewidth=1, alpha=0.7, label='被过滤信号', zorder=3)

        # 添加虚线连接被过滤信号点到对应的突破线
        for signal in turtle.filtered_signals:
            ax2.plot([signal['date'], signal['date']],
                    [signal['price'], signal['signal_price']],
                    color='gray', linestyle=':', alpha=0.5, linewidth=1)

    ax2.set_title('股价与交易信号', fontsize=14, fontweight='bold')
    ax2.set_ylabel('价格 (元)')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)

    # 3. 回撤分析图
    equity_series = pd.Series(turtle.equity_curve, index=df.index)
    peak = equity_series.cummax()
    drawdown = (peak - equity_series) / peak * 100

    ax3.fill_between(df.index, 0, -drawdown, color='red', alpha=0.3, label='回撤')
    ax3.plot(df.index, -drawdown, color='red', linewidth=1)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    max_dd = drawdown.max()
    max_dd_date = drawdown.idxmax()
    ax3.scatter([max_dd_date], [-max_dd], color='red', s=100, zorder=5)
    ax3.annotate(f'最大回撤: {max_dd:.1f}%',
                xy=(max_dd_date, -max_dd), xytext=(10, 10),
                textcoords='offset points', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

    ax3.set_title('回撤分析', fontsize=14, fontweight='bold')
    ax3.set_xlabel('日期')
    ax3.set_ylabel('回撤 (%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 打印交易统计
    print_trade_statistics(turtle)


def print_trade_statistics(turtle):
    """打印详细的交易统计信息"""
    buy_trades = [t for t in turtle.trade_log if t['action'] == 'BUY']
    sell_trades = [t for t in turtle.trade_log if t['action'] == 'SELL']
    partial_sell_trades = [t for t in turtle.trade_log if t['action'] == 'PARTIAL_SELL']

    if not sell_trades and not partial_sell_trades:
        print("\n📊 交易统计: 暂无完成的交易")
        return

    print(f"\n{'='*60}")
    print(f"📊 详细交易统计")
    print(f"{'='*60}")

    # 盈亏统计
    winning_trades = [t for t in sell_trades if t.get('return_rate', 0) > 0]
    losing_trades = [t for t in sell_trades if t.get('return_rate', 0) <= 0]

    total_profit = sum([t.get('profit_loss', 0) for t in winning_trades])
    total_loss = sum([t.get('profit_loss', 0) for t in losing_trades])

    print(f"盈利交易: {len(winning_trades)}笔, 总盈利: {total_profit:+,.2f}元")
    print(f"亏损交易: {len(losing_trades)}笔, 总亏损: {total_loss:+,.2f}元")
    print(f"净盈亏: {total_profit + total_loss:+,.2f}元")

    if len(winning_trades) > 0:
        avg_win = total_profit / len(winning_trades)
        max_win = max([t.get('profit_loss', 0) for t in winning_trades])
        print(f"平均盈利: {avg_win:+,.2f}元, 最大盈利: {max_win:+,.2f}元")

    if len(losing_trades) > 0:
        avg_loss = total_loss / len(losing_trades)
        max_loss = min([t.get('profit_loss', 0) for t in losing_trades])
        print(f"平均亏损: {avg_loss:+,.2f}元, 最大亏损: {max_loss:+,.2f}元")

    # 盈亏比
    if len(losing_trades) > 0 and total_loss != 0:
        profit_loss_ratio = abs(total_profit / total_loss) if total_loss != 0 else 0
        print(f"盈亏比: {profit_loss_ratio:.2f}")

    # 出场原因统计
    print(f"\n📋 出场原因统计:")
    exit_reasons = {}
    all_exit_trades = sell_trades + partial_sell_trades
    for trade in all_exit_trades:
        reason = trade.get('reason', '未知')
        exit_reasons[reason] = exit_reasons.get(reason, 0) + 1

    for reason, count in exit_reasons.items():
        percentage = count / len(all_exit_trades) * 100
        print(f"  {reason}: {count}笔 ({percentage:.1f}%)")

    # 止盈减仓统计
    if partial_sell_trades:
        print(f"\n💰 止盈减仓统计:")
        print(f"  止盈减仓次数: {len(partial_sell_trades)}次")
        partial_profit = sum([t.get('profit_loss', 0) for t in partial_sell_trades])
        print(f"  止盈减仓总盈利: {partial_profit:+,.2f}元")

        # 按级别统计
        level_stats = {}
        for trade in partial_sell_trades:
            reason = trade.get('reason', '')
            if 'N止盈减仓' in reason:
                level = reason.split('N')[0]
                level_stats[level] = level_stats.get(level, 0) + 1

        for level, count in level_stats.items():
            print(f"  {level}N止盈减仓: {count}次")

    # 持仓时间统计
    print(f"\n⏱️ 持仓时间分析:")
    holding_periods = []
    for i, sell_trade in enumerate(sell_trades):
        # 找到对应的买入交易
        sell_date = sell_trade['date']
        buy_date = None
        for buy_trade in reversed(buy_trades[:i+1]):
            if '入场' in buy_trade.get('reason', ''):
                buy_date = buy_trade['date']
                break

        if buy_date:
            holding_days = (sell_date - buy_date).days
            holding_periods.append(holding_days)

    if holding_periods:
        avg_holding = np.mean(holding_periods)
        max_holding = max(holding_periods)
        min_holding = min(holding_periods)
        print(f"  平均持仓: {avg_holding:.1f}天")
        print(f"  最长持仓: {max_holding}天, 最短持仓: {min_holding}天")


def get_data(code:str, start_date:str, end_date:str):
    try:
        if code.startswith('5'):
            df = pro.fund_daily(ts_code=code, start_date=start_date, end_date=end_date)
        else:
            df = pro.daily(ts_code=code, start_date=start_date, end_date=end_date)
        print(f"成功获取数据，共{len(df)}行")
        
        if df.empty:
            print("获取的数据为空")
            return None
            
        df = df.sort_values('trade_date')
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        print(f"日期范围: {df.index.min()} 到 {df.index.max()}")
        return df
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return None


def main():
    print("开始获取数据...")
    df = get_data('002324.SZ', '20231001', '20250827')
    if df is None:
        print("❌ 数据获取失败，程序退出")
        return

    print(f"✅ 数据获取成功，数据形状: {df.shape}")
    print(f"📅 数据日期范围: {df.index.min()} 到 {df.index.max()}")
    print(f"📊 数据列: {list(df.columns)}")

    # 检查数据质量
    print(f"🔍 数据质量检查:")
    print(f"   - 缺失值: {df.isnull().sum().sum()}")
    print(f"   - 价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")

    # 创建收盘价突破+布林带过滤海龟策略
    print("\n🐢 创建收盘价突破+布林带过滤海龟策略实例...")
    turtle = OptimizedTurtleStrategy(
        initial_capital=30000,
        risk_per_trade=0.01,
        max_units=4
    )

    # 执行回测
    print("\n🚀 开始执行回测...")
    df = turtle.backtest(df)

    # 检查交易记录
    print(f"\n📈 回测完成，交易记录数量: {len(turtle.trade_log)}")
    if turtle.trade_log:
        print("前5笔交易:")
        for i, trade in enumerate(turtle.trade_log[:5]):
            print(f"  {i+1}. {trade['date'].strftime('%Y-%m-%d')} {trade['action']} {trade['price']:.2f} {trade.get('reason', '')}")
    else:
        print("⚠️ 没有产生任何交易记录")

        # 分析为什么没有交易
        print("\n🔍 分析无交易原因:")
        df_analysis = turtle.calculate_indicators(df)

        # 检查信号
        entry_signals = df_analysis['sys1_entry_signal'].dropna()
        if entry_signals.empty:
            print("   - 入场信号计算失败")
        else:
            print(f"   - 入场信号范围: {entry_signals.min():.2f} - {entry_signals.max():.2f}")

        # 检查是否有突破
        breakthrough_count = 0
        for i, row in df_analysis.iterrows():
            if pd.notna(row['sys1_entry_signal']) and row['high'] > row['sys1_entry_signal']:
                breakthrough_count += 1
                if breakthrough_count <= 3:  # 只显示前3个
                    print(f"   - 发现突破: {i.strftime('%Y-%m-%d')} 高点{row['high']:.2f} > 信号{row['sys1_entry_signal']:.2f}")

        if breakthrough_count == 0:
            print("   - 整个回测期间没有发现入场信号突破")
        else:
            print(f"   - 总共发现 {breakthrough_count} 次突破")

    # 生成报告
    print("\n📋 生成回测报告...")
    turtle.print_results()

    # 绘制可视化图表
    try:
        print("\n📊 绘制图表...")
        plot_results(df, turtle)
        print("✅ 图表显示完成")
    except Exception as e:
        print(f"❌ 图表显示失败: {e}")

    print(f"\n✅ 回测完成！")


if __name__ == "__main__":
    main()
