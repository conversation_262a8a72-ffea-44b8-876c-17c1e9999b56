import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 1. 设置Tushare Token
your_tushare_token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'  # 请替换为你的真实Token
ts.set_token(your_tushare_token)
pro = ts.pro_api()

# 2. 获取历史数据 - 以沪深300ETF（510300.SH）为例
try:
    df = pro.daily(ts_code='003021.SZ', start_date='20230101', end_date='20240527', )
    print(f"成功获取数据，共{len(df)}行")
    
    if df.empty:
        print("获取的数据为空，请检查股票代码和日期范围")
        
    df = df.sort_values('trade_date') # 确保数据按日期升序排列
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df.set_index('trade_date', inplace=True)
    print(f"日期范围: {df.index.min()} 到 {df.index.max()}")
    
except Exception as e:
    print(f"获取数据时出错: {e}")


# 3. 海龟策略计算核心指标
# 计算真实波动幅度（TR）和N值（ATR，20日均值）
df['prev_close'] = df['close'].shift(1)
df['high_low'] = df['high'] - df['low']
df['high_prevclose'] = np.abs(df['high'] - df['prev_close'])
df['low_prevclose'] = np.abs(df['low'] - df['prev_close'])
df['TR'] = df[['high_low', 'high_prevclose', 'low_prevclose']].max(axis=1)
df['N'] = df['TR'].rolling(window=20).mean()

# 回测使用的指标（位移1天，避免前视偏差）
df['20d_high_signal'] = df['high'].rolling(window=20).max().shift(1)
df['10d_low_signal'] = df['low'].rolling(window=10).min().shift(1)

# 绘图使用的指标（不位移，显示真实极值）
df['20d_high_plot'] = df['high'].rolling(window=20).max()
df['10d_low_plot'] = df['low'].rolling(window=10).min()

# 4. 初始化回测变量
initial_cash = 30000  # 3万元初始本金
cash = initial_cash
position = 0           # 当前持仓股数
units = 0              # 当前持仓单位数
entry_price = 0        # 入场价格
trade_log = []         # 交易记录
equity_curve = []      # 每日资产净值记录

# 5. 模拟交易循环
print("开始回测...")
for i, row in df.iterrows():
    # 跳过NaN值（前期数据不足计算指标）
    if pd.isna(row['N']) or pd.isna(row['20d_high_signal']) or pd.isna(row['10d_low_signal']):
        equity_curve.append(cash + position * row['close'])
        continue
        
    current_close = row['close']
    current_high = row['high']
    current_low = row['low']

    current_n = row['N']
    atr = current_n
    
    # 信号判断（使用位移后的信号）
    buy_signal_sys1 = (current_high > row['20d_high_signal']) and (units < 4)
    sell_signal = (current_low < row['10d_low_signal']) and (position > 0)

    # 交易执行逻辑
    if sell_signal:
        # 平仓：卖出所有持仓
        cash += position * row['10d_low_signal']
        trade_log.append({'date': i, 'action': 'SELL', 'price': row['10d_low_signal'], 'units': units, 'cash': cash})
        position = 0
        units = 0
        entry_price = 0

    elif buy_signal_sys1 and cash > 0 and atr > 0:
        # 计算风险：目标是让1N的波动等于本金的1%
        risk_per_unit = atr
        unit_value = initial_cash * 0.01 / risk_per_unit # 1%本金风险对应的股数
        unit_value = int(unit_value / 100) * 100  # A股按手交易，简化按100股取整

        if unit_value > 0:
            # 计算本次可买入的股数（1个单位）
            shares_to_buy = unit_value
            # 确保资金足够
            cost = shares_to_buy * row['20d_high_signal']
            if cost <= cash:
                cash -= cost
                position += shares_to_buy
                units += 1
                entry_price = row['20d_high_signal'] if entry_price == 0 else (entry_price * (units-1) + current_close) / units # 计算平均入场价
                trade_log.append({'date': i, 'action': 'BUY', 'price': row['20d_high_signal'], 'units': units, 'cash': cash})

    # 记录每日净资产
    daily_equity = cash + position * current_close
    equity_curve.append(daily_equity)

# 6. 检查equity_curve是否有数据
if not equity_curve:
    print("错误: equity_curve为空，没有进行任何回测计算")
    exit()

# 7. 将回测结果添加到DataFrame
df = df.iloc[:len(equity_curve)]  # 确保df与equity_curve长度一致
df['equity'] = equity_curve

# 8. 输出最终结果和交易记录
final_equity = equity_curve[-1]
total_return = (final_equity - initial_cash) / initial_cash * 100
print(f"\n初始资金: {initial_cash:.2f} 元")
print(f"最终资产: {final_equity:.2f} 元")
print(f"总收益率: {total_return:.2f}%")
print(f"交易次数: {len(trade_log)}")

if trade_log:
    print("\n交易记录:")
    for trade in trade_log:
        print(f"日期: {trade['date'].strftime('%Y-%m-%d')}, 操作: {trade['action']}, "
              f"价格: {trade['price']:.2f}, 单位: {trade['units']}, 现金: {trade['cash']:.2f}")
else:
    print("没有发生任何交易")

# 9. 绘制资金曲线和股价对比
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# 资金曲线
ax1.plot(df.index, df['equity'], label='资金曲线', linewidth=1.5, color='blue')
ax1.set_title('海龟交易策略回测 - 资金曲线')
ax1.set_ylabel('资产价值 (元)')
ax1.legend()
ax1.grid(True)

# 股价和交易信号
ax2.plot(df.index, df['close'], label='股价', linewidth=1.5, color='black', alpha=0.8)
ax2.plot(df.index, df['20d_high_plot'], label='20日高点', linestyle='--', linewidth=1.2, color='red', alpha=0.8)
ax2.plot(df.index, df['10d_low_plot'], label='10日低点', linestyle='--', linewidth=1.2, color='green', alpha=0.8)

# 标记买入卖出点
buy_dates = [trade['date'] for trade in trade_log if trade['action'] == 'BUY']
sell_dates = [trade['date'] for trade in trade_log if trade['action'] == 'SELL']
buy_prices = [trade['price'] for trade in trade_log if trade['action'] == 'BUY']
sell_prices = [trade['price'] for trade in trade_log if trade['action'] == 'SELL']

if buy_dates:
    ax2.scatter(buy_dates, buy_prices, color='green', marker='^', s=100, label='买入点', zorder=5)
if sell_dates:
    ax2.scatter(sell_dates, sell_prices, color='red', marker='v', s=100, label='卖出点', zorder=5)

ax2.set_title('股价与交易信号')
ax2.set_xlabel('日期')
ax2.set_ylabel('价格 (元)')
ax2.legend()
ax2.grid(True)

plt.tight_layout()
plt.show()

# 10. 输出策略性能指标
if len(equity_curve) > 1:
    returns = pd.Series(equity_curve).pct_change().dropna()
    max_drawdown = (pd.Series(equity_curve).cummax() - pd.Series(equity_curve)).max() / pd.Series(equity_curve).cummax().max()
    
    print(f"\n策略性能指标:")
    print(f"年化收益率: {returns.mean() * 252 * 100:.2f}%")  # 假设252个交易日
    print(f"最大回撤: {max_drawdown * 100:.2f}%")
    print(f"夏普比率: {returns.mean() / returns.std() * np.sqrt(252):.2f}")  # 简化计算