import akshare as ak
import pandas as pd
from typing import List
from decimal import Decimal
from datetime import datetime
from .data_models import StockData

def get_stock_data(stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从 AKShare 获取股票历史数据
    :param stock_code: 股票代码 (如 "600000")
    :param start_date: 开始日期 (格式 "YYYYMMDD")
    :param end_date: 结束日期 (格式 "YYYYMMDD")
    :return: 包含 OHLCV 数据的 DataFrame
    """
    try:
        # 获取后复权数据（包含分红送股）
        df = ak.stock_zh_a_hist(
            symbol=stock_code, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            adjust="qfq"
        )
        
        # 规范列名
        df = df.rename(columns={
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume'
        })
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 计算平均成交量（20日移动平均）
        df['avg_vol'] = df['volume'].rolling(window=20, min_periods=1).mean()
        
        return df[['date', 'open', 'high', 'low', 'close', 'volume', 'avg_vol']]
    
    except Exception as e:
        raise RuntimeError(f"获取 {stock_code} 数据失败: {str(e)}")

def dataframe_to_stock_data(df: pd.DataFrame, symbol: str) -> List[StockData]:
    """将DataFrame转换为StockData列表"""
    stock_data_list = []
    
    for _, row in df.iterrows():
        stock_data = StockData(
            symbol=symbol,
            date=row['date'].to_pydatetime(),
            open=Decimal(str(row['open'])),
            high=Decimal(str(row['high'])),
            low=Decimal(str(row['low'])),
            close=Decimal(str(row['close'])),
            volume=int(row['volume'])
        )
        stock_data_list.append(stock_data)
    
    return stock_data_list