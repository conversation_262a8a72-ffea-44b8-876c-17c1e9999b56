from dataclasses import dataclass, asdict
from decimal import Decimal, getcontext
from typing import Optional, List, Dict
import json
from datetime import datetime, timedelta

from stock_backtest.data_loader import get_stock_data, dataframe_to_stock_data
from stock_backtest.data_models import StockData
from stock_visualization import StockVisualizer

getcontext().prec = 28  # Decimal 精度

# StockData 类现在从 stock_backtest.data_models 导入

# ----------- ZigZag 点结构（便于持久化与回看） -----------
@dataclass
class ZigZagPoint:
    date: str
    point_type: str      # 'HIGH'/'LOW'/'PENDING_HIGH'/'PENDING_LOW'
    price: Decimal       # 对于 HIGH 用最高价；LOW 用最低价
    source: str = "zz"   # 你可存来源/备注

# ----------- 每个 symbol 的状态（可JSON化） -----------
@dataclass
class ZigZagState:
    symbol: str

    # 参数（每只股票可统一或各自配置）
    atr_period: int = 14
    atr_multiplier: Decimal = Decimal('1.5')
    min_price_move: Decimal = Decimal('0')
    min_trend_bars: int = 1

    # ATR 滚动（Wilder）
    prev_close: Optional[Decimal] = None
    atr: Optional[Decimal] = None
    atr_count: int = 0
    tr_sum: Decimal = Decimal('0')   # 前 period 根TR之和，用于首个ATR

    # 当前趋势
    trend: Optional[str] = None      # 'UP'/'DOWN'/None
    bars_in_trend: int = 0

    # 趋势内极值（随着趋势推进不断更新）
    extreme_high: Optional[Decimal] = None
    extreme_high_date: Optional[str] = None
    extreme_low: Optional[Decimal] = None
    extreme_low_date: Optional[str] = None

    # 已确认拐点 & 末尾PENDING拐点
    confirmed_points: List[ZigZagPoint] = None
    pending_point: Optional[ZigZagPoint] = None

    def __post_init__(self):
        if self.confirmed_points is None:
            self.confirmed_points = []

    # ---- 序列化辅助 ----
    def to_dict(self) -> Dict:
        d = asdict(self)
        # Decimal -> str
        for k in ["atr", "tr_sum", "atr_multiplier", "min_price_move",
                  "extreme_high", "extreme_low"]:
            if d[k] is not None:
                d[k] = str(d[k])
        # points Decimal -> str
        for p in d["confirmed_points"]:
            p["price"] = str(p["price"])
        if d["pending_point"] is not None:
            d["pending_point"]["price"] = str(d["pending_point"]["price"])
        return d

    @staticmethod
    def from_dict(d: Dict) -> "ZigZagState":
        # 反序列化 Decimal
        def D(x): return None if x is None else Decimal(x)
        s = ZigZagState(
            symbol=d["symbol"],
            atr_period=d.get("atr_period", 14),
            atr_multiplier=D(d.get("atr_multiplier", "1.5")),
            min_price_move=D(d.get("min_price_move", "0")),
            min_trend_bars=d.get("min_trend_bars", 1),
            prev_close=D(d.get("prev_close")),
            atr=D(d.get("atr")),
            atr_count=d.get("atr_count", 0),
            tr_sum=D(d.get("tr_sum", "0")),
            trend=d.get("trend"),
            bars_in_trend=d.get("bars_in_trend", 0),
            extreme_high=D(d.get("extreme_high")) if d.get("extreme_high") is not None else None,
            extreme_high_date=d.get("extreme_high_date"),
            extreme_low=D(d.get("extreme_low")) if d.get("extreme_low") is not None else None,
            extreme_low_date=d.get("extreme_low_date"),
            confirmed_points=[],
            pending_point=None
        )
        for p in d.get("confirmed_points", []):
            s.confirmed_points.append(
                ZigZagPoint(p["date"], p["point_type"], Decimal(p["price"]), p.get("source", "zz"))
            )
        if d.get("pending_point") is not None:
            pp = d["pending_point"]
            s.pending_point = ZigZagPoint(pp["date"], pp["point_type"], Decimal(pp["price"]), pp.get("source", "zz"))
        return s


# ----------- 内部工具：TR/ATR增量更新（Wilder） -----------
def _update_atr(state: ZigZagState, bar: StockData):
    high, low, close = bar.high, bar.low, bar.close
    if state.prev_close is None:
        tr = high - low
    else:
        tr = max(high - low, abs(high - state.prev_close), abs(low - state.prev_close))

    # 初始化阶段：累计 period 根 TR，得到首个 ATR
    if state.atr_count < state.atr_period:
        state.tr_sum += tr
        state.atr_count += 1
        if state.atr_count == state.atr_period:
            state.atr = (state.tr_sum / Decimal(state.atr_period))
    else:
        # Wilder 平滑
        state.atr = ((state.atr * Decimal(state.atr_period - 1)) + tr) / Decimal(state.atr_period)

    state.prev_close = close
    


# ----------- 内部工具：验证局部极值 -----------
def _is_local_extreme(stock_data_list: List[StockData], index: int, point_type: str, window: int = 3) -> bool:
    """
    验证指定索引的点是否为局部极值
    """
    if index < 0 or index >= len(stock_data_list):
        return False

    start_idx = max(0, index - window)
    end_idx = min(len(stock_data_list), index + window + 1)

    if point_type == "HIGH":
        window_highs = [stock_data_list[j].high for j in range(start_idx, end_idx)]
        return stock_data_list[index].high == max(window_highs)
    else:  # LOW
        window_lows = [stock_data_list[j].low for j in range(start_idx, end_idx)]
        return stock_data_list[index].low == min(window_lows)


# ----------- 内部工具：更新趋势状态（高效版本） -----------
def _update_trend_status_from_point(stock_data_list: List[StockData], from_date: str, trend: str, symbol: str):
    """
    从指定日期开始更新趋势状态，避免遍历整个列表
    """
    if not stock_data_list:
        return

    # 找到起始索引（从后往前找，因为通常是最近的日期）
    start_idx = -1
    for i in range(len(stock_data_list) - 1, -1, -1):
        stock_date_str = stock_data_list[i].date.strftime('%Y-%m-%d') if hasattr(stock_data_list[i].date, 'strftime') else str(stock_data_list[i].date)
        if stock_date_str == from_date and stock_data_list[i].symbol == symbol:
            start_idx = i
            break

    # 从找到的位置开始更新到列表末尾
    if start_idx >= 0:
        for i in range(start_idx, len(stock_data_list)):
            if stock_data_list[i].symbol == symbol:
                stock_data_list[i].trend_status = trend


# ----------- 内部工具：确认拐点（实时验证版本，无需后处理） -----------
def _confirm_point(state: ZigZagState, point_type: str, date: str, price: Decimal, stock_data_list: List[StockData] = None):
    """
    确认拐点，实时验证局部极值，避免连续同类型点
    """
    # 实时验证局部极值 - 这样就不需要后处理了
    if stock_data_list:
        # 找到对应的索引
        target_idx = -1
        for i, stock_data in enumerate(stock_data_list):
            stock_date_str = stock_data.date.strftime('%Y-%m-%d') if hasattr(stock_data.date, 'strftime') else str(stock_data.date)
            if stock_date_str == date and stock_data.symbol == state.symbol:
                target_idx = i
                break

        # 实时验证局部极值 - 如果不是局部极值就直接跳过
        if target_idx >= 0 and not _is_local_extreme(stock_data_list, target_idx, point_type, window=5):
            # 不是局部极值，直接跳过，无需后处理
            return

    # 检查是否会产生连续的同类型点
    if state.confirmed_points and state.confirmed_points[-1].point_type == point_type:
        last = state.confirmed_points[-1]

        # 如果是同类型，只有在更极端的情况下才替换
        should_replace = False
        if point_type == "HIGH":
            should_replace = price > last.price
        else:  # LOW
            should_replace = price < last.price

        if should_replace:
            # 先清除旧点的标记
            if stock_data_list:
                _clear_zigzag_point_marking(stock_data_list, last.date, state.symbol)

            # 更新为更极端的点
            state.confirmed_points[-1] = ZigZagPoint(date, point_type, price)
        else:
            # 不替换，直接返回
            return
    else:
        # 添加新的拐点
        state.confirmed_points.append(ZigZagPoint(date, point_type, price))

    # 更新对应的 StockData 对象
    if stock_data_list:
        # 标记新的zigzag点
        for stock_data in stock_data_list:
            stock_date_str = stock_data.date.strftime('%Y-%m-%d') if hasattr(stock_data.date, 'strftime') else str(stock_data.date)
            if stock_date_str == date and stock_data.symbol == state.symbol:
                stock_data.is_zigzag_point = True
                stock_data.zigzag_point_type = point_type
                break

        # 更新趋势状态（高效版本）
        if len(state.confirmed_points) >= 2:
            last_two = state.confirmed_points[-2:]
            current_trend = None
            if last_two[0].point_type == "LOW" and last_two[1].point_type == "HIGH":
                current_trend = "UP"
            elif last_two[0].point_type == "HIGH" and last_two[1].point_type == "LOW":
                current_trend = "DOWN"

            if current_trend:
                _update_trend_status_from_point(stock_data_list, date, current_trend, state.symbol)

    state.pending_point = None  # 确认后清掉 pending


# ----------- 内部工具：清除zigzag点标记 -----------
def _clear_zigzag_point_marking(stock_data_list: List[StockData], date: str, symbol: str):
    """
    清除指定日期的zigzag点标记
    """
    for stock_data in stock_data_list:
        stock_date_str = stock_data.date.strftime('%Y-%m-%d') if hasattr(stock_data.date, 'strftime') else str(stock_data.date)
        if stock_date_str == date and stock_data.symbol == symbol:
            stock_data.is_zigzag_point = False
            stock_data.zigzag_point_type = None
            break


# ----------- 增量更新主函数：喂一根bar推进状态 -----------
def update_state(state: ZigZagState, bar: StockData, stock_data_list: List[StockData] = None) -> ZigZagState:
    assert state.symbol == bar.symbol, "状态与bar的symbol不一致"

    # 1) 更新 ATR
    _update_atr(state, bar)

    # 2) ATR 未就绪，先积累数据、初始化极值与pending
    if state.atr is None:
        # 初始化极值
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        # 暂时给个 pending（不会确认）
        if state.pending_point is None:
            # 用离收盘更近的方向先立个 pending
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
                state.trend = "DOWN"  # 倾向向下
            else:
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
                state.trend = "UP"    # 倾向向上
        state.bars_in_trend += 1
        return state

    # 3) 正常阶段：根据趋势推进极值 & 判断是否反转
    if state.trend is None:
        # 根据当前bar与极值决定初始趋势，并给 pending
        if state.extreme_high is None or bar.high > state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
        if state.extreme_low is None or bar.low < state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)

        rng = state.extreme_high - state.extreme_low
        if rng >= state.atr * state.atr_multiplier and rng >= state.min_price_move:
            if (state.extreme_high - bar.close) > (bar.close - state.extreme_low):
                state.trend = "DOWN"
                state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", state.extreme_high)
            else:
                state.trend = "UP"
                state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", state.extreme_low)
            state.bars_in_trend = 1
        else:
            state.bars_in_trend += 1
        return state

    # 已有趋势
    state.bars_in_trend += 1

    if state.trend == "UP":
        # 更新上升趋势内的最高点（pending_high）
        if state.extreme_high is None or bar.high >= state.extreme_high:
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

        # 检查是否触发反转为 DOWN（满足 ATR 回撤 + 最小波动 + 最小条数）
        if (state.extreme_high - bar.low) >= state.atr * state.atr_multiplier and \
           (state.extreme_high - bar.low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            # 确认 HIGH 拐点（把 pending_high 变成 HIGH）
            _confirm_point(state, "HIGH", state.extreme_high_date, state.extreme_high, stock_data_list)
            # 切换趋势到 DOWN，并以当前bar.low为新的 pending_low 起点
            state.trend = "DOWN"
            state.bars_in_trend = 1
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

    elif state.trend == "DOWN":
        # 更新下降趋势内的最低点（pending_low）
        if state.extreme_low is None or bar.low <= state.extreme_low:
            state.extreme_low = bar.low
            state.extreme_low_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_low_date, "PENDING_LOW", bar.low)

        # 检查是否触发反转为 UP
        if (bar.high - state.extreme_low) >= state.atr * state.atr_multiplier and \
           (bar.high - state.extreme_low) >= state.min_price_move and \
           state.bars_in_trend >= state.min_trend_bars:
            # 确认 LOW 拐点
            _confirm_point(state, "LOW", state.extreme_low_date, state.extreme_low, stock_data_list)
            # 切换趋势到 UP
            state.trend = "UP"
            state.bars_in_trend = 1
            state.extreme_high = bar.high
            state.extreme_high_date = bar.date.strftime('%Y-%m-%d') if hasattr(bar.date, 'strftime') else str(bar.date)
            state.pending_point = ZigZagPoint(state.extreme_high_date, "PENDING_HIGH", bar.high)

    return state


# 注意：不再需要后处理函数 - 所有验证都在 _confirm_point 中实时完成


# ----------- 批量更新（股票池） -----------
def update_pool(states: Dict[str, ZigZagState], today_bars: List[StockData]) -> Dict[str, ZigZagState]:
    """
    states: {symbol -> ZigZagState}（从DB取出来反序列化即可）
    today_bars: 多只股票同日期的bar列表
    返回：更新后的 states（也可就地更新，然后持久化）
    """
    for bar in today_bars:
        if bar.symbol not in states:
            states[bar.symbol] = ZigZagState(symbol=bar.symbol)  # 用默认参数初始化
        states[bar.symbol] = update_state(states[bar.symbol], bar)
    return states



# 可以选择使用真实数据或演示数据
USE_REAL_DATA = True  # 设置为True使用真实数据，False使用演示数据

if USE_REAL_DATA:
    # 真实数据
    stock_code = '002324'
    start_date ='20190701'
    end_date = '20250820'
    df = get_stock_data(stock_code, start_date, end_date)
    stock_data_list = dataframe_to_stock_data(df, stock_code)
else:
    # 演示数据
    def create_demo_data():
        """创建演示数据"""
        import random

        random.seed(42)  # 固定随机种子
        base_date = datetime(2025, 7, 1)
        stock_data_list = []

        # 模拟真实的价格走势
        base_price = 30.0
        current_price = base_price

        for day in range(100):  # 100天数据
            # 添加趋势和噪音
            if day < 30:
                trend = 0.008  # 上升趋势
            elif day < 70:
                trend = -0.006  # 下降趋势
            else:
                trend = 0.005  # 再次上升

            # 添加随机噪音
            noise = random.uniform(-0.03, 0.03)
            daily_change = trend + noise

            # 计算OHLC
            open_price = current_price
            close_price = current_price * (1 + daily_change)

            # 日内波动
            volatility = 0.015
            high_price = max(open_price, close_price) * (1 + random.uniform(0, volatility))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, volatility))

            date = base_date + timedelta(days=day)
            stock_data = StockData(
                symbol="DEMO001",
                date=date,
                open=Decimal(f"{open_price:.2f}"),
                high=Decimal(f"{high_price:.2f}"),
                low=Decimal(f"{low_price:.2f}"),
                close=Decimal(f"{close_price:.2f}"),
                volume=random.randint(800000, 1200000)
            )
            stock_data_list.append(stock_data)
            current_price = close_price

        return stock_data_list

    stock_data_list = create_demo_data()
    stock_code = "DEMO001"

# ---- 股票池状态，通常从数据库加载；第一次运行时为空 ----
states = {}

# 模拟逐日更新
for i in range(len(stock_data_list)):
    symbol = stock_data_list[i].symbol
    if symbol not in states:
        states[symbol] = ZigZagState(symbol=symbol) 
    st = update_state(states[symbol], stock_data_list[i], stock_data_list)



    print(f"股票 {symbol} 状态：")
    print("  已确认拐点：")
    for p in st.confirmed_points:
        print(f"    {p.date} {p.point_type} {p.price}")
    if st.pending_point:
        print(f"  末尾pending点：{st.pending_point.date} {st.pending_point.point_type} {st.pending_point.price}")
    print("  当前趋势：", st.trend)
    print("  已走的bar数：", st.bars_in_trend)

    # 显示当前bar的zigzag信息
    current_bar = stock_data_list[i]
    if current_bar.is_zigzag_point:
        print(f"  当前bar是zigzag点：{current_bar.date} {current_bar.zigzag_point_type} 趋势:{current_bar.trend_status}")
    print()


print("验证结果...")

def validate_zigzag_points(data: List[StockData], window: int = 5):
    """
    验证ZigZag点是否确实是局部极值
    """
    print("\n=== 验证ZigZag点 ===")
    for i, point in enumerate(data):
        if point.is_zigzag_point:
            # 检查窗口范围内是否确实是极值
            start_idx = max(0, i - window)
            end_idx = min(len(data), i + window + 1)
            
            if point.zigzag_point_type == 'HIGH':
                window_highs = [data[j].high for j in range(start_idx, end_idx)]
                is_valid = point.high == max(window_highs)
                print(f"索引{i} HIGH点 {point.high}: {'✓' if is_valid else '✗'} "
                      f"(窗口{start_idx}-{end_idx-1}最高{max(window_highs)})")
                
            elif point.zigzag_point_type == 'LOW':
                window_lows = [data[j].low for j in range(start_idx, end_idx)]
                is_valid = point.low == min(window_lows)
                print(f"索引{i} LOW点 {point.low}: {'✓' if is_valid else '✗'} "
                      f"(窗口{start_idx}-{end_idx-1}最低{min(window_lows)})")

# 验证优化效果
def check_optimization_results(stock_data_list: List[StockData], states: Dict[str, ZigZagState]):
    """检查优化效果"""
    print("\n=== 优化效果检查 ===")

    for symbol, state in states.items():
        print(f"\n股票 {symbol}:")
        print(f"  总确认拐点数: {len(state.confirmed_points)}")

        # 检查连续同类型点
        consecutive_count = 0
        if len(state.confirmed_points) > 1:
            for i in range(1, len(state.confirmed_points)):
                if state.confirmed_points[i].point_type == state.confirmed_points[i-1].point_type:
                    consecutive_count += 1

        print(f"  连续同类型点: {consecutive_count} 个")

        # 检查局部极值有效性
        valid_count = 0
        total_points = 0
        for i, data in enumerate(stock_data_list):
            if data.is_zigzag_point and data.symbol == symbol:
                total_points += 1
                if _is_local_extreme(stock_data_list, i, data.zigzag_point_type, window=5):
                    valid_count += 1

        if total_points > 0:
            validity_rate = valid_count / total_points
            print(f"  局部极值有效性: {valid_count}/{total_points} ({validity_rate:.1%})")
        else:
            print(f"  局部极值有效性: 无ZigZag点")

        # 显示拐点序列
        print(f"  拐点序列:")
        for i, point in enumerate(state.confirmed_points):
            print(f"    {i+1}. {point.date} {point.point_type} {point.price}")

check_optimization_results(stock_data_list, states)
validate_zigzag_points(stock_data_list)

print("正在生成可视化图表...")
    
# 创建可视化
visualizer = StockVisualizer()
output_file = f"{stock_code}_analysis_demo.html"
filename = visualizer.save_chart(stock_data_list, output_file, stock_code)

