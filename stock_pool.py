import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def is_gem_stock(stock_code):
    """
    判断是否为创业板股票
    创业板股票代码以300开头
    """
    return str(stock_code).startswith('300')

def is_st_stock(stock_name):
    """
    判断是否为ST股票（简单判断）
    """
    return 'ST' in stock_name or '*ST' in stock_name

def get_hot_concepts_stock_pool():
    """
    从热点板块中筛选优质股票池
    """
    try:
        # 1. 获取热点概念板块
        print("正在获取热点概念板块...")
        concept_df = ak.stock_board_concept_name_em()
        
        # 选择涨幅前5的热点概念（避免过于集中）
        hot_concepts = concept_df.nlargest(5, '涨跌幅')
        print(f"热门概念板块: {list(hot_concepts['板块名称'])}")
        
        all_candidates = []
        
        # 2. 对每个热点概念进行分析
        for _, concept_row in hot_concepts.iterrows():
            concept_name = concept_row['板块名称']
            concept_change = concept_row['涨跌幅']
            
            print(f"\n分析概念板块: {concept_name} (涨幅: {concept_change}%)")
            
            # 获取概念成分股
            try:
                concept_members_df = ak.stock_board_concept_cons_em(symbol=concept_name)
                
                if concept_members_df.empty:
                    continue
                
                # 3. 剔除创业板和ST股票
                filtered_df = concept_members_df.copy()
                filtered_df = filtered_df[~filtered_df['代码'].apply(is_gem_stock)]
                filtered_df = filtered_df[~filtered_df['名称'].apply(is_st_stock)]
                
                print(f"剔除创业板和ST后剩余股票数量: {len(filtered_df)}")
                
                if filtered_df.empty:
                    print(f"概念 {concept_name} 剔除后无剩余股票")
                    continue
                
                # 4. 对每个成分股进行基本面和技术面分析
                concept_stocks = analyze_concept_stocks(filtered_df, concept_name)
                all_candidates.extend(concept_stocks)
                
            except Exception as e:
                print(f"获取概念 {concept_name} 成分股失败: {e}")
                continue
        
        # 5. 综合评分并筛选前10支优质股票
        if not all_candidates:
            print("未找到符合条件的股票")
            return pd.DataFrame()
        
        final_df = pd.DataFrame(all_candidates)
        
        # 按综合评分排序
        final_df = final_df.sort_values('综合评分', ascending=False)
        
        # 选择前10支股票
        selected_stocks = final_df.head(10)
        
        print("\n" + "="*80)
        print("最终筛选出的10支优质股票 (已剔除创业板和ST):")
        print("="*80)
        
        for i, (_, stock) in enumerate(selected_stocks.iterrows(), 1):
            print(f"{i}. {stock['股票名称']}({stock['股票代码']}) - "
                  f"评分: {stock['综合评分']:.2f}, "
                  f"概念: {stock['所属概念']}, "
                  f"涨幅: {stock['近期涨幅']}%")
        
        return selected_stocks[['股票代码', '股票名称', '所属概念', '综合评分', '近期涨幅', 
                              '市盈率', '市净率', '市值', '换手率']]
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        return pd.DataFrame()

def analyze_concept_stocks(concept_members_df, concept_name):
    """
    分析概念板块中的个股
    """
    stocks_data = []
    
    for _, stock_row in concept_members_df.iterrows():
        stock_code = stock_row['代码']
        stock_name = stock_row['名称']
        
        # 再次检查是否为创业板（双重保险）
        if is_gem_stock(stock_code) or is_st_stock(stock_name):
            continue
            
        try:
            # 获取个股基本面数据
            stock_info = get_stock_basic_info(stock_code)
            if stock_info is None:
                continue
            
            # 获取技术面数据
            tech_data = get_technical_indicators(stock_code)
            if tech_data is None:
                continue
            
            # 计算综合评分
            composite_score = calculate_composite_score(stock_info, tech_data)
            
            stock_data = {
                '股票代码': stock_code,
                '股票名称': stock_name,
                '所属概念': concept_name,
                '综合评分': composite_score,
                '近期涨幅': stock_row['涨跌幅'],
                '市盈率': stock_info.get('pe_ttm', 0),
                '市净率': stock_info.get('pb', 0),
                '市值': stock_info.get('market_cap', 0),
                '换手率': stock_row['换手率']
            }
            
            stocks_data.append(stock_data)
            
        except Exception as e:
            print(f"分析股票 {stock_name}({stock_code}) 时出错: {e}")
            continue
    
    return stocks_data

def get_stock_basic_info(stock_code):
    """
    获取股票基本面信息
    """
    try:
        # 获取个股基本信息
        stock_info = ak.stock_individual_info_em(symbol=stock_code)
        if stock_info.empty:
            return None
        
        # 转换为字典格式
        info_dict = {}
        for _, row in stock_info.iterrows():
            info_dict[row['item']] = row['value']
        
        # 获取市值信息
        market_cap = 0
        try:
            stock_zh_a_spot_df = ak.stock_zh_a_spot_em()
            spot_info = stock_zh_a_spot_df[stock_zh_a_spot_df['代码'] == stock_code]
            if not spot_info.empty:
                market_cap = spot_info.iloc[0]['总市值']
        except:
            pass
        
        # 提取关键指标
        basic_info = {
            'pe_ttm': float(info_dict.get('市盈率(动)', 0)) if info_dict.get('市盈率(动)', '0') != '-' else 0,
            'pb': float(info_dict.get('市净率', 0)) if info_dict.get('市净率', '0') != '-' else 0,
            'market_cap': market_cap
        }
        
        return basic_info
        
    except Exception as e:
        print(f"获取股票 {stock_code} 基本面信息失败: {e}")
        return None

def get_technical_indicators(stock_code):
    """
    获取技术指标
    """
    try:
        # 获取近期股价数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=60)).strftime('%Y%m%d')
        
        stock_data = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                      start_date=start_date, end_date=end_date, 
                                      adjust="qfq")
        
        if stock_data.empty:
            return None
        
        # 计算简单技术指标
        recent_data = stock_data.tail(20)  # 最近20个交易日
        
        tech_indicators = {
            'volume_ratio': recent_data['成交量'].mean() / stock_data['成交量'].mean() if stock_data['成交量'].mean() > 0 else 1,
            'price_trend': (recent_data['收盘'].iloc[-1] / recent_data['收盘'].iloc[0] - 1) * 100,
            'volatility': recent_data['收盘'].std() / recent_data['收盘'].mean() * 100 if recent_data['收盘'].mean() > 0 else 0
        }
        
        return tech_indicators
        
    except Exception as e:
        print(f"获取股票 {stock_code} 技术指标失败: {e}")
        return None

def calculate_composite_score(basic_info, tech_data):
    """
    计算综合评分
    """
    score = 0
    
    # 估值评分 (0-30分)
    pe = basic_info.get('pe_ttm', 100)
    pb = basic_info.get('pb', 5)
    
    if 0 < pe < 30:
        score += (30 - pe) / 30 * 15  # PE越低分越高
    if 0 < pb < 3:
        score += (3 - pb) / 3 * 15    # PB越低分越高
    
    # 技术面评分 (0-40分)
    volume_ratio = tech_data.get('volume_ratio', 1)
    price_trend = tech_data.get('price_trend', 0)
    
    if volume_ratio > 1.5:
        score += 20  # 成交量活跃
    elif volume_ratio > 1:
        score += 10
    
    if price_trend > 5:
        score += 20  # 近期上涨趋势
    elif price_trend > 0:
        score += 10
    
    # 市值评分 (0-30分) - 偏好中小市值
    market_cap = basic_info.get('market_cap', 1000)
    if market_cap < 50:  # 50亿以下
        score += 30
    elif market_cap < 100:
        score += 20
    elif market_cap < 200:
        score += 10
    
    return min(score, 100)  # 确保不超过100分

# 运行筛选程序
if __name__ == "__main__":
    print("开始筛选优质股票池（已剔除创业板和ST股票）...")
    stock_pool = get_hot_concepts_stock_pool()
    
    if not stock_pool.empty:
        print("\n筛选完成！优质股票池:")
        print(stock_pool.to_string(index=False))
        
        # 保存结果到CSV文件
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"优质股票池_{timestamp}.csv"
            stock_pool.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n结果已保存到: {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")
    else:
        print("未能成功筛选出股票池")